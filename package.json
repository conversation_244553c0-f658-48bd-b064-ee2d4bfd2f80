{"name": "fe_capstone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "cleanup": "node scripts/cleanup.js", "health-check": "node scripts/health-check.js", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.1", "glsl-canvas-js": "^0.2.9", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "ldrs": "^1.1.7", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "zod": "^3.25.28"}, "devDependencies": {"@eslint/js": "^9.22.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^23.0.1", "tw-animate-css": "^1.2.8", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vitest": "^3.1.4"}}